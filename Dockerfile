FROM node:carbon-jessie

RUN apt-get update &&\
apt-get install libzmq3-dev openssh-client git python build-essential -y

WORKDIR /root

RUN mkdir /root/.ssh
RUN printf -- "-----BEGIN RSA PRIVATE KEY-----\n\
MIIEpAIBAAKCAQEA1fX9ewBO0W8WE8UusbZTK57tquL9JBuIjtdmy9IXILWNbKts\n\
+6Y3935ntmASPMhYIVdJpfwYhz/LjvXNKEUb6HFolwbaq4a33QPFxZ2rOmtceZQ+\n\
a46AvbU1R3mVHM2X6Vqi0bTvp3UfzqXOgd5mhPKc8UFMYQqjn7Vvy2oalsbV1cM5\n\
EoVhgtd6D+9zPSIZFBfhK/dWX5/nbfr2shke2tDCvFW+w7Jj6hJgL44ZYI3sLRbr\n\
GgthKW63F+jsITQakg1fNYsg3w2ds0baJ4NgyCrgCq91X1sUL9YQ3yENlMWrvSm6\n\
XDNetkSSh3W6mFDn5C+neRxYI3DOjbbX1vYjTQIDAQABAoIBADDZqBUUPeqdVUoQ\n\
RBf5C8Yy1AqnJqq+yo/qaCLzBarHKNH91bUi/zY3mxBNoPxVtaFvH3aRNzxx+VTb\n\
lAq0tKl+cyXAP97XtFT2zonO9NAywPtVOGcwMv6dR9vYy4229w8lTXVZvTnbiPaS\n\
brLCB2AlUGXYxcqGNlN5hlvIDugLdIU8IeHzLpmgqpINGP+fsQzVksPXsGtwx6Mg\n\
fcF/Ar0tpE6XaQ9dQVcVtfBw9az94bSX7i1BUJtpmPzcWYZCdpvwNrtdw14JOyMQ\n\
cXjOg0QHgnMBO1DThtLWkpw+8uXN2iUi9JKY1cqUK6e/CHx+TXjOi6CorsjDbK0Q\n\
i+VK0IECgYEA/TylXq3a7T3bl+hfdN7b1yslWsG7DV9I7PRlou4FRzpwcMUTj0LZ\n\
lboEIrXroTesJV4krH7A5m9w1HIQdVI0yf4eu5gv3Dx/YeJQwi1cu2e69xShT4Kk\n\
gTLGetnPqjIg4r/+yNiPO97sWRWfYQnQnDfUV8+paZ2h7vK8KPKRc60CgYEA2Eui\n\
8OPVv0/t1WnL7NYgmz5TujapXef5K04kRhidzd8FvM6G87qRG3IPjp6xthya99n0\n\
GKOLzazMG9of3rNH+nZJGW77prTaEwYOq4CtojyL7Re7IVPLKYGP8jk72H/wOA9V\n\
7QWyH9/QbWN9ANryexR/gZCiwrMBk53VJaFmYiECgYEAhhzcJ6xDSWl9JpI2G5r7\n\
KpT7D3mSKp7P7E3ylbsgqHxWmCKHHVP2D8mX7xoRVCw7yv50xbXLSz6xrpFKDhgf\n\
ROqENC2BC04u2BQXWP7VIZrp9XjDhWxW2uWdH+rChAxQRkYQFSY7lSWWXgeJhMbB\n\
6DnZBzg/LGQDIp0C8Vylk3ECgYAWgHfpG6rHBme1xhXjA7vMqsVHIByqmlF/wddg\n\
O3tfSY5tQAcmIAislEcuDTBrw7oTguc0EP+gXBO36g9aucgJNHkSXktr4nC1ffHU\n\
wcROloHwHoHnS7JdFQ+GoZGqSlURX/cOe4M2cbBbUPhWOHD1gQJq1kM4girt0oO0\n\
MIBYQQKBgQCRpNNCGJu3F0YAzjgPuA8GmJ7ZZYmWhaWIF9uu5azQg7Fy8Ludvw8G\n\
KnsstJJdeMNdmrMddUNykGuzObTr5XbxPMR4YaM0nL7n5g0f3uqxEeigS2sXjomK\n\
W4/mtqm74jTLoEXaYBu1nsKTQVtL8gDP7aKMrVycZJwNOqtEN7Esmg==\n\
-----END RSA PRIVATE KEY-----" >> /root/.ssh/id_rsa
RUN printf "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDV9f17AE7RbxYTxS6xtlMrnu2q4v0kG4iO12bL0hcgtY1sq2z7pjf3fme2YBI8yFghV0ml/BiHP8uO9c0oRRvocWiXBtqrhrfdA8XFnas6a1x5lD5rjoC9tTVHeZUczZfpWqLRtO+ndR/Opc6B3maE8pzxQUxhCqOftW/LahqWxtXVwzkShWGC13oP73M9IhkUF+Er91Zfn+dt+vayGR7a0MK8Vb7DsmPqEmAvjhlgjewtFusaC2EpbrcX6OwhNBqSDV81iyDfDZ2zRtong2DIKuAKr3VfWxQv1hDfIQ2Uxau9KbpcM162RJKHdbqYUOfkL6d5HFgjcM6NttfW9iNN jenkins@smbig5" >> /root/.ssh/id_rsa.pub

RUN printf "[www1.percipio-big-data.com]:221,[***********]:221 ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBLqRNT3dMpPhsD6Qr4NMTDp2CC9jrJXeiBiaehOzqEvVy1lc/7ZYthtjHhwaPqiWhoMP9qq2XQvCIP0iEpoZ9YY="  >> /root/.ssh/known_hosts

RUN chmod -R 700 /root/.ssh

RUN mkdir /root/app
WORKDIR /root/app

COPY . .

# Environment Variables
ENV NODE_ENV develop

EXPOSE 9010

RUN npm install
CMD [ "npm", "start" ]