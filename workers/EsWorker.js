'use strict';

const fs = require('fs');
const config = require('ms_config');
const winston = require('winston');
const logger = require('logger')(config, fs, winston);
const tracelog =  require('efk_logger').init(config, fs, winston);
const ZmqWorker = require('pigato').Worker;
const ZmqClient = require('pigato').Client;
const thriftLib = require('thrift_lib');
const worker = new ZmqWorker(config.zmq.brokerAddress, 'es', {concurrency: -1});
const zmqClient = new ZmqClient(config.zmq.brokerAddress);
const Promise = require('bluebird');
const elasticsearch = require('elasticsearch');
const cassandra = require('cassandra-driver');
const cassandraAuthProvider = new cassandra.auth.PlainTextAuthProvider(config.db.username, config.db.password);
const client = new cassandra.Client({ contactPoints: config.db.hosts, keyspace: config.db.keyspace, authProvider : cassandraAuthProvider, queryOptions: config.db.queryOptions, protocolOptions: { port: config.db.port } });
const clientNews = new cassandra.Client({ contactPoints: config.db_news.hosts, keyspace: config.db_news.keyspace, authProvider : cassandraAuthProvider, queryOptions: config.db_news.queryOptions, protocolOptions: { port: config.db.port } });
const moment = require('moment');
const dateformat = require('date-format');
const defFormat = 'yyyy-MM-dd';
const defMonthFormat = 'yyyy-MM';
const ident = 'es';

const ES_SEARCH_TIMEOUT = 40000;
const MAX_WORDS = 1500;
const MAX_SUMMARY_LEN = MAX_WORDS * 6;

const esMap = new Map();
const esIndexMap = new Map();

const EsIndexType = {
    DOCUMENT: 1,
    NEWS: 2,
    TWEET: 3,
    TWITTER_USER: 4,
    SOURCE: 5,
    DEFAULT: 100
};

// map es configuration to map
config.es.forEach((es) => {
    es.client = elasticsearch.Client({hosts: es.hosts, log: 'error', requestTimeout: ES_SEARCH_TIMEOUT});
    esMap.set(es.type, es);
});

config.esIndex.forEach((esIndex) => {
    esIndexMap.set(esIndex.name, esIndex);
});

function error2string(error) {
    if (error instanceof Error) return error.stack;
    else return error;
}

// promisified elasticsearch search api method
var promiseSearchEs = function(esClient, params, msg) {
    return new Promise(function(resolve, reject) {
        esClient.search(params, function(err, result) {
            if (err) {
                tracelog.error(ident, msg.type, msg.reqId, msg.sessionId, 'Execute search es promise failed', err);
                reject(err);
                return;
            }
            resolve(result);
        });
    });
};

// promisified elasticsearch search api method
var promiseGetEs = function(esClient, params, msg) {
    return new Promise(function(resolve, reject) {
        esClient.get(params, function(err, result) {
            if (err) tracelog.warn(ident, msg.type, msg.reqId, msg.sessionId, 'Execute get es promise failed', params, err);
            resolve(result);
        });
    });
};

client.connect(function(err) {
    if (err) {
        tracelog.error(ident, 0, 0, 0, 'EsWorker failed to connect to cassandra %s/%s, error', config.db.hosts, config.db.keyspace, err);
        client.shutdown();
        return;
    }
    tracelog.info(ident, 0, 0, 0, 'EsWorker connected to cassandra %s/%s', config.db.hosts, config.db.keyspace, JSON.stringify(client.hosts));
    // start client and worker
    tracelog.info(ident, 0, 0, 0, 'EsWorker connecting to %s', config.zmq.brokerAddress);
    zmqClient.start();
    worker.start();
});

worker.on('error', function(err) {
    tracelog.error(ident, 0, 0, 0, 'EsWorker error', err);
});

worker.on('connect', function() {
    tracelog.info(ident, 0, 0, 0, 'EsWorker connected to', config.zmq.brokerAddress);
});

worker.on('disconnect', function() {
    tracelog.info(ident, 0, 0, 0, 'EsWorker disconnected from', config.zmq.brokerAddress);
});

const ProcType = {
  EMPTY_RESP: 0,
  REQ_RESP: 1,
  NO_RESP: 2
};

function getReqFuncAndProcType(type) {
    var funcs = {
        [thriftLib.msgTypes.MsgType.SEARCH_ES_REQ]: {func: searchEsReq, respType: thriftLib.msgTypes.MsgType.SEARCH_ES_RESP, procType: ProcType.REQ_RESP},
        [thriftLib.msgTypes.MsgType.GET_ES_REQ]: {func: getEsReq, respType: thriftLib.msgTypes.MsgType.GET_ES_RESP, procType: ProcType.REQ_RESP},
        [thriftLib.msgTypes.MsgType.EMPTY]: {func: dummyFunc, procType: ProcType.NO_RESP},
        [thriftLib.msgTypes.MsgType.ERROR]: {func: dummyFunc, procType: ProcType.NO_RESP}
    }
    return funcs[type];
}

function dummyFunc(msg, msgOrig, cb) {
    cb(null);
}

worker.on('request', function(data, res, opts) {
    var type = undefined;
    var reqId = undefined;
    var sessionId = undefined;
    try {
        var buf = new Buffer(data); 
        var msg = thriftLib.deserializeMsg(buf);
        // remember message type and reqId in case of exception
        type = msg.type;
        reqId = msg.reqId;
        sessionId = msg.sessionId;
        if (msg.type === thriftLib.msgTypes.MsgType.ERROR) tracelog.error(ident, msg.type, msg.reqId, msg.sessionId, 'Received ERROR msg', msg.value.errorDesc);
        //else tracelog.info(ident, msg.type, msg.reqId, msg.sessionId, 'Received msg', JSON.stringify(msg));
        var funcProcType = getReqFuncAndProcType(type);
        if (funcProcType) {
            funcProcType.func(msg.value, msg, function(err, msgResp) {
                if (funcProcType.procType == ProcType.REQ_RESP) {
                    if (err) sendErrorV2(res, thriftLib.msgTypes.ErrorCode.GENERIC, err, msg);
                    else sendMsgV2(res, funcProcType.respType, msgResp, msg);
                } else if (funcProcType.procType == ProcType.EMPTY_RESP) {
                    if (err) sendErrorV2(res, thriftLib.msgTypes.ErrorCode.GENERIC, err, msg);
                    else sendEmptyV2(res, msg);
                }
            });
        } else {
            // send EMPTY response anyway
            tracelog.error(ident, msg.type, msg.reqId, msg.sessionId, 'Received msg %d is not processed [%s]. Sending EMPTY response.', msg.mType, thriftLib.getMsgLogId(msg));
            sendEmptyV2(res, msg);
        }
    } catch (e) {
        let err = error2string(e);
        tracelog.error(ident,type, reqId, sessionId, 'Failed to process received msg', err);
        if (type) {
            sendErrorV2(res, thriftLib.msgTypes.ErrorCode.GENERIC, e.message, msg);
        } else {
            if (msg) {
                msg.type = thriftLib.msgTypes.MsgType.EMPTY;
                sendErrorV2(res, thriftLib.msgTypes.ErrorCode.GENERIC, e.message, msg);
            } else tracelog.error(ident,type, reqId, sessionId, 'Failed to respond to undefined msg', err);
        }
    }
});

function composeNewsSearchJson(search) {
    const must = [];
    const should = [];
    const mustNot = [];
    const sort = [];
    let fields = [];
    if (search.searchType === 'news-cloud')
        fields = ['lemmas'];
    else {
        fields = ['lemmas', 'title'];
    }
    if (search.search != null) {
        search.search.forEach(function(s) {
            let o = null;
            switch (s.type) {
                case thriftLib.esTypes.SearchDataType.KEYWORD: {
                    //o = {'query_string': {'query': 'title:\"'+ s.value + '\"'}};
                    o = {'multi_match': {'query': s.value, 'fields': fields}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) must.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) should.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNot.push(o);
                }
                    break;
                case thriftLib.esTypes.SearchDataType.SOURCE_ID: {
                    o = {'query_string': {'query': 'sourceId:\"'+ s.value + '\"'}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) must.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) should.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNot.push(o);
                }
                    break;
            }
        });
    }
    //if (must.length > 0) {
        if (search.range !== null) {
            if (search.range.type === 'year') {
                must.push({'range': {'year': {'gte': parseInt(search.range.gte), 'lte': parseInt(search.range.lte)}}});
            } else if (search.range.type === 'date') {
                must.push({'range': {'date': {'gte': search.range.gte, 'lte': search.range.lte}}});
            }
        }
    //}
    if (search.sortType === 'date') {
        sort.push({'date': {'order': search.sortOrder}});
    } else if (search.sortType === 'timeline') {
        sort.push({'date': {'order': search.sortOrder}});
        //    sort.push('journal');
    } else if (search.sortType === 'year') {
        sort.push({'year': {'order': search.sortOrder}});
    }
    sort.push('_score');
    let jsonObject;
    if (search.searchType === 'news-cloud') {
        const agg = {'our_agg': {'significant_terms': {'field':'summary', 'size': search.size, 'min_doc_count':2}}};
        jsonObject = {'query': {'bool': {'must': must, 'should': should, 'must_not': mustNot}}, 'aggs': agg};
    } else
        jsonObject = {'query': {'bool': {'must': must, 'should': should, 'must_not': mustNot}}, 'sort': sort};
    jsonObject._source = ['id', 'type', 'title', 'date', 'year', 'journal', 'url', 'geohash'];
    return jsonObject;
}

function aggType2Name(type) {
    switch(type) {
        case thriftLib.esTypes.AggregationType.TWITTER_USER_ID:
            return 'userId';
        case thriftLib.esTypes.AggregationType.TWITTER_TAGS:
            return 'tags';
        case thriftLib.esTypes.AggregationType.FOS:
            return 'fos.keyword';
        case thriftLib.esTypes.AggregationType.KEYWORDS:
            return 'keywords.keyword';
        case thriftLib.esTypes.AggregationType.YEAR:
            return 'year';
        case thriftLib.esTypes.AggregationType.PUBLISHER:
            return 'publisher.keyword';
    }
    return 'undefined';
}

function name2AggType(name) {
    switch(name) {
        case 'userId':
            return thriftLib.esTypes.AggregationType.TWITTER_USER_ID;
        case 'tags':
            return thriftLib.esTypes.AggregationType.TWITTER_TAGS;
        case 'fos.keyword':
            return thriftLib.esTypes.AggregationType.FOS;
        case 'keywords.keyword':
            return thriftLib.esTypes.AggregationType.KEYWORDS;
        case 'year':
            return thriftLib.esTypes.AggregationType.YEAR;
        case 'publisher.keyword':
            return thriftLib.esTypes.AggregationType.PUBLISHER;
    }
    return null;
}

function composeSearchJson(search, type, indexType) {
    const must = [];
    const should = [];
    const mustNot = [];
    const mustNestedName = [];
    const shouldNestedName = [];
    const mustNotNestedName = [];
    const mustNestedOrg = [];
    const shouldNestedOrg = [];
    const mustNotNestedOrg = [];
    const sort = [];
    if (search.search != null) {
        search.search.forEach(function(s) {
            let o = null;
            switch (s.type) {
                case thriftLib.esTypes.SearchDataType.KEYWORD: {
                    o = {'query_string': {'query': 'lemmas:\"'+ s.value + '\"'}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) must.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) should.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNot.push(o);
                }
                break;
                case thriftLib.esTypes.SearchDataType.AUTHOR: {
                    o = {'query_string': {'query': 'authors.name:\"'+ s.value + '\"'}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) mustNestedName.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) shouldNestedName.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNotNestedName.push(o);
                }
                break;
                case thriftLib.esTypes.SearchDataType.ORG: {
                    o = {'query_string': {'query': 'authors.org:\"'+ s.value + '\"'}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) mustNestedName.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) shouldNestedName.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNotNestedName.push(o);
                }
                break;
                case thriftLib.esTypes.SearchDataType.ASSIGNEE: {
                    o = {'query_string': {'query': 'assignee.name:\"'+ s.value + '\"'}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) mustNestedName.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) shouldNestedName.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNotNestedName.push(o);
                }
                break;
                case thriftLib.esTypes.SearchDataType.FOS: {
                    o = {'query_string': {'query': 'fos:\"'+ s.value + '\"'}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) must.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) should.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNot.push(o);
                }
                break;
                case thriftLib.esTypes.SearchDataType.CHANNEL: {
                    o = {'query_string': {'query': 'channels:\"'+ s.value + '\"'}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) must.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) should.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNot.push(o);
                }
                break;
                case thriftLib.esTypes.SearchDataType.TWEET_TEXT: {
                    o = {'query_string': {'query': 'tweetText:\"'+ s.value + '\"'}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) must.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) should.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNot.push(o);
                }
                break;
                case thriftLib.esTypes.SearchDataType.SOURCE_ID: {
                    o = {'query_string': {'query': 'sourceId:\"'+ s.value + '\"'}};
                    if (s.operation == thriftLib.esTypes.SearchOperation.MUST) must.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.SHOULD) should.push(o);
                    else if (s.operation == thriftLib.esTypes.SearchOperation.MUST_NOT) mustNot.push(o);
                }
                break;
            }
        });
    }
    if ((type != null) && (type.length == 1)) {
        const o = {'query_string': {'query': 'type:\"'+ type[0] + '\"'}};
        must.push(o);
    }
    if (search.range !== null) {
        if (search.range.type === 'year') {
            must.push({'range': {'year': {'gte': parseInt(search.range.gte), 'lte': parseInt(search.range.lte)}}});
        } else if (search.range.type === 'date') {
            must.push({'range': {'date': {'gte': search.range.gte, 'lte': search.range.lte}}});
        } else if (search.range.type === 'createdTime') {
            must.push({'range': {'createdTime': {'gte': search.range.gte, 'lte': search.range.lte}}});
        }
    }
    if (search.sortType === 'timeline') {
        sort.push({'date': {'order': search.sortOrder}});
    } else if (search.sortType) {
        sort.push({[search.sortType]: {'order': search.sortOrder}});
    }
    sort.push('_score');
    let jsonObject;
    if (search.searchType === 'news-cloud') {
        const agg = {'our_agg': {'significant_terms': {'field':'summary', 'size': search.size, 'min_doc_count':2}}};
        jsonObject = {'query': {'bool': {'must': must, 'should': should, 'must_not': mustNot}}, 'aggs': agg};
    } else {
        if ((mustNestedName.length > 0) || (shouldNestedName.length > 0) || (mustNotNestedName.length > 0))
            must.push({'nested': {'path': 'authors', 'query': {'bool': {'must': mustNestedName, 'should': shouldNestedName, 'must_not': mustNotNestedName}}}});
        if ((mustNestedOrg.length > 0) || (shouldNestedOrg.length > 0) || (mustNotNestedOrg.length > 0))
            must.push({'nested': {'path': 'authors', 'query': {'bool': {'must': mustNestedOrg, 'should': shouldNestedOrg, 'must_not': mustNotNestedOrg}}}});
        if (search.geohashRange != null)
            must.push({'geo_bounding_box': {'geohash':{'top_left': search.geohashRange.tl, 'bottom_right': search.geohashRange.br}}});
        // check if must is empty then force all under must
        if (must.length == 0) must.push({'wildcard':{'id':{'value':'*'}}});
        jsonObject = {'query': {'bool': {'must': must, 'should': should, 'must_not': mustNot}}, 'sort': sort};
    }
    if (search.aggregation && (search.aggregation.length > 0)) {
        const agg = new Object();
        search.aggregation.forEach(a => {const aName = aggType2Name(a); agg[aName] = {'terms': {'field': aName}}});
        jsonObject.aggs = agg;
    }
    switch (indexType) {
        case EsIndexType.NEWS:
        case EsIndexType.TWEET:
        case EsIndexType.TWITTER_USER:
            break;
        default:
            if (search.geohashRange != null) {
                // geo query therefore reduce it just to important field
                jsonObject._source = ['id', 'type', 'title', 'date', 'year', 'journal', 'urls', 'authors', 'geohash'];
            }
            break;
    }
    return jsonObject;
}

function listContains(list, item) {
    if (list == null)
        return false;
    for (var i = 0; i < list.length; i++) {
        if (list[i] == item) {
            return true;
        }
    }
    return false;
}

function getEs(search) {
    let searchType = 'general';
    if (listContains(search.type, 'news') == true) searchType = 'news';
    else if (listContains(search.type, 'sources') == true) searchType = 'sources';
    else if (listContains(search.type, 'twitter') == true) searchType = 'twitter';
    return esMap.get(searchType);
}

function getEsIndexDesc(es, search) {
    let esIndex = (es.index) ? es.index : search.indexName;
    let esIndexDesc = esIndexMap.get(esIndex);
    if (!esIndexDesc) {
        esIndexDesc = {indexType: EsIndexType.DEFAULT, indexName: esIndex};
    }
    return esIndexDesc;
}

async function updateSearchStat(search, msg, esIndex, cb) {
    tracelog.debug(ident, msg.type, msg.reqId, msg.sessionId, `Updating search stat ${esIndex} ${JSON.stringify(search)}`);
    if (search.geohashRange) return null;
    const createdTime = new Date();
    const createdDate = dateformat(defFormat, createdTime);
    const searchKeywords = search.search.map(s => s.value);
    const promises = searchKeywords.map(keyword => client.execute('UPDATE keyword_queries SET count = count + 1 WHERE keyword = ? AND created_date = ?',
        [keyword, createdDate]), { prepare: true });
    promises.push(client.execute('INSERT INTO session_queries (id, created_date, created_time, search_index, keywords, search) values (?, ?, ?, ?, ?, ?)',
        [msg.sessionId, createdDate, createdTime, esIndex, searchKeywords, thriftLib.serializeMsgImpl(search)], { prepare: true }));
    return Promise.all(promises);
}

function getNewsIndex(indexName, date) {
    const mDate = moment(date.getTime());
    return `${indexName}_${mDate.format(defMonthFormat)},${indexName}_${mDate.subtract(1, 'month').format(defMonthFormat)},${indexName}_${mDate.subtract(1, 'month').format(defMonthFormat)}`;
}

function searchEsReq(search, msg, cb) {
    // get es data
    let es = getEs(search);
    let esIndexDesc = getEsIndexDesc(es, search);
    tracelog.debug(ident, msg.type, msg.reqId, msg.sessionId, `Es search request ${JSON.stringify(msg)} ${JSON.stringify(esIndexDesc)}`);
    updateSearchStat(search, msg, esIndexDesc.indexName).then(() => {
        // compose json
        let jsonQuery = null;
        switch (esIndexDesc.indexType) {
            case EsIndexType.NEWS:
                tracelog.debug(ident, msg.type, msg.reqId, msg.sessionId, `Composing old search ${esIndexDesc.indexType}`);
                jsonQuery = composeNewsSearchJson(search);
                break;
            case EsIndexType.TWEET:
            case EsIndexType.TWITTER_USER:
                jsonQuery = composeSearchJson(search, null, esIndexDesc.indexType);
                break;
            default:
                jsonQuery = composeSearchJson(search, search.type, esIndexDesc.indexType);
                break;
        }
        let params = { index: esIndexDesc.indexName, type: [], from: search.start, size: search.size, body: jsonQuery };

        if (esIndexDesc.indexType === EsIndexType.NEWS) {
            params.index = getNewsIndex(esIndexDesc.indexName, new Date());
        }

        tracelog.debug(ident, msg.type, msg.reqId, msg.sessionId, `Sending es search request ${es.hosts} ${JSON.stringify(params)}`);
        return promiseSearchEs(es.client, params, msg)
    }).then((result) => {
        tracelog.debug(ident, msg.type, msg.reqId, msg.sessionId, `Parsing es search response data`);
        return parseSearchResultEs(msg, esIndexDesc.indexType, result);
    }).then((msgResp) => {
        msgResp.start = search.start;
        //tracelog.debug(ident, msg.type, msg.reqId, msg.sessionId, `Sending es search response ${JSON.stringify(msgResp)}`);
        tracelog.debug(ident, msg.type, msg.reqId, msg.sessionId, `Sending es search response`);
        cb(null, msgResp);
    }).catch(err => {
        tracelog.error(ident, msg.type, msg.reqId, msg.sessionId, `Es search request failed ${error2string(err)}`);
        cb(err);
    });
}

async function parseResultEs(msg, indexType, result) {
    if (result.hits) {
        const documents = [];
        const promises = [];
        const map = new Map();
        let aggs = undefined;
        if (result.hits.hits) {
            for (const hit of result.hits.hits) {
                let doc = null;
                switch (indexType) {
                    case EsIndexType.NEWS: {
                            doc = new thriftLib.esTypes.Document(hit._source);
                            map.set(doc.id, doc);
                            promises.push(clientNews.execute('SELECT id, summary, source FROM news WHERE id = ? AND date = ?',
                                [doc.id, doc.date], { prepare: true }));
                        }
                        break;
                    case EsIndexType.TWEET:
                        doc = new thriftLib.esTypes.Tweet(hit._source);
                        break;
                    case EsIndexType.TWITTER_USER:
                        doc = new thriftLib.esTypes.TwitterUser(hit._source);
                        break;
                    default:
                        doc = new thriftLib.esTypes.Document2(hit._source);
                        // we don't need information about lemmas
                        doc.lemmas = null;
                        break;
                }
                documents.push(doc);
            }
        }
        if (promises.length > 0) {
            const results = await Promise.all(promises);
            for (const result of results) {
                if (result.rows.length) {
                    const row = result.rows[0];
                    const doc = map.get(row.id);
                    doc.summary = (!row.summary || (row.summary.length <= MAX_SUMMARY_LEN)) ? row.summary : row.summary.substring(0, MAX_SUMMARY_LEN);
                    doc.journal = row.source;
                }
            }
        }
        if (result.aggregations) {
            aggs = [];
            for (let a in result.aggregations) {
                let aggType = name2AggType(a);
                let aggRes = new thriftLib.esTypes.AggregationResult({type: aggType, keys: []});
                aggRes.keys = result.aggregations[a].buckets.map(b => new thriftLib.esTypes.AggregationKey({key: (b.key_as_string) ? b.key_as_string : b.key, docCount: b.doc_count}));
                aggs.push(aggRes);
            }
        }
        return {total: result.hits.total, docs: documents, aggs: aggs};
    }
    return {total: 0, docs: []};
}

async function parseSearchResultEs(msg, indexType, result) {
    const ret = await parseResultEs(msg, indexType, result);
    const msgResp = new thriftLib.esTypes.SearchEsResp({ total: ret.total, documents: [], documents2: [], tweets: [], twitterUsers: [] });
    switch (indexType) {
        case EsIndexType.NEWS:
            msgResp.documents = ret.docs;
            break;
        case EsIndexType.TWEET:
            msgResp.tweets = ret.docs;
            break;
        case EsIndexType.TWITTER_USER:
            msgResp.twitterUsers = ret.docs;
            break;
        default:
            msgResp.documents2 = ret.docs;
            break;
    }
    if (ret.aggs) msgResp.aggResults = ret.aggs;
    return msgResp;
}

function getEsReq(search, msg, cb) {
    // get es data
    let type = search.type;
    if (type) search.type = [type];
    else search.type = [];
    let es = getEs(search);
    let esIndexDesc = getEsIndexDesc(es, search);
    tracelog.debug(ident, msg.type, msg.reqId, msg.sessionId, 'Sending es get request %s %s', es.hosts, esIndexDesc.indexName, search.type);
    var promises = [];
    search.ids.forEach(function(id) {
        promises.push(promiseGetEs(es.client, {index: esIndexDesc.indexName, type: 'doc', id: id}, msg));
    });
    Promise.all(promises).then(function(results) {
        cb(null, parseGetResultEs(esIndexDesc.indexType, results));
    }).catch(function(err) {
        cb(err, null)
    });
}

function parseGetResultEs(indexType, results) {
    var msgResp = new thriftLib.esTypes.GetEsResp({documents: [], tweets: [], twitterUsers: []});
    results.forEach(function(result) {
        if (result.found == true) {
            switch (indexType) {
                case EsIndexType.TWEET:
                    msgResp.tweets.push(new thriftLib.esTypes.Tweet(result._source));
                    break;
                case EsIndexType.TWITTER_USER:
                    msgResp.twitterUsers.push(new thriftLib.esTypes.TwitterUser(result._source));
                    break;
                default:
                    msgResp.documents.push(new thriftLib.esTypes.Document2(result._source));
                    break;
            }
        }
    });
    return msgResp;
}

function sendEmpty(res, reqId, sessionId) {
    sendMessage(res, thriftLib.msgTypes.MsgType.EMPTY, null, reqId, sessionId);
}

function getErr(err) {
    return err.message ? err.message : err.toString();
}

function sendError(res, errCode, errDesc, type, reqId, sessionId) {
    var msg = new thriftLib.msgTypes.ErrorMsg();
    msg.code = errCode;
    msg.errorDesc = getErr(errDesc);
    msg.msgType = type;
    tracelog.error(ident, type, reqId, sessionId, 'Sending ERROR msg', JSON.stringify(msg));
    sendMessage(res, thriftLib.msgTypes.MsgType.ERROR, msg, reqId, sessionId);
}

function sendMessage(res, type, data, reqId, sessionId)  {
    var buf = thriftLib.serializeMsg(type, data, reqId, sessionId);
    res.end(buf, function(err) {
        if (err) tracelog.error(ident, type, reqId, sessionId, 'Failed to write response', err.toString());
    });
}

function sendEmptyV2(res, msg) {
    sendMsgV2(res, thriftLib.msgTypes.MsgType.EMPTY, null, msg);
}

function sendErrorV2(res, errCode, errDesc, msg) {
    var msgResp = new thriftLib.msgTypes.ErrorMsg();
    msgResp.code = errCode;
    msgResp.errorDesc = getErr(errDesc);
    msgResp.msgType = msg.type;
    sendMsgV2(res, thriftLib.msgTypes.MsgType.ERROR, msgResp, msg);
}

function sendMsgV2(res, type, msgResp, msg) {
    var buf = thriftLib.serializeMsgV2({type: type, value: msgResp, reqId: msg.reqId, sessionId: msg.sessionId, sessionToken: msg.sessionToken});
    res.end(buf, function(err) {
        if (err) tracelog.error(ident, msg.type, msg.reqId, msg.sessionId, 'Failed to write response', err.toString());
    });
}
