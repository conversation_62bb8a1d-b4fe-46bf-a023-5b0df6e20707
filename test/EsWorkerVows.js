'use strict';

require('app-module-path').addPath(__dirname);

var config = require('ms_config');
var ZmqClient = require('pigato').Client;
var thriftLib = require('thrift_lib');
var vows = require('vows');
var should = require('should');

var suite = vows.describe('ES tests');
var client = new ZmqClient(config.zmq.brokerAddress);

client.start();

var searchEsReq = new thriftLib.esTypes.SearchEsReq({'size': 20, 'start': 0, 'indexName': 'document', 'type': ['article'],
    'sortType': 'year', 'sortOrder': 'asc', 'range': null,
    'search': [{'value':'abstract','type':1,'operation':2}]});
var searchEsReqTest = {
    'SEARCH ES REQ TEST': {
        topic: function() {
            test(thriftLib.serializeMsg(thriftLib.msgTypes.MsgType.SEARCH_ES_REQ, searchEsReq, 2235, '6179266c-be35-4e20-a1ee-0547e5c7fb88'), this.callback);
        }, 'Result': function(err, result) {
            should.not.exist(err);
            result.type.should.not.be.equal(thriftLib.msgTypes.MsgType.ERROR);
            result.type.should.be.equal(thriftLib.msgTypes.MsgType.SEARCH_ES_RESP);
            console.log('SEARCH ES REQ PASSED');
        }
    }
};

var searchEsTwitterReq = new thriftLib.esTypes.SearchEsReq({size: 20, start: 0, indexName: 'tweets', type: [],
    sortType: 'createdTime', sortOrder: 'desc', range: null,
    //search: [{'value':'teacher','type':1,'operation':2}], aggregation: [1, 2]
    search: [{'value':'teacher','type':1,'operation':2}]
});
var searchEsTwitterReqTest = {
    'SEARCH ES TWITTER REQ TEST': {
        topic: function() {
            test(thriftLib.serializeMsg(thriftLib.msgTypes.MsgType.SEARCH_ES_REQ, searchEsTwitterReq, 2235, '6179266c-be35-4e20-a1ee-0547e5c7fb88'), this.callback);
        }, 'Result': function(err, result) {
            should.not.exist(err);
            result.type.should.not.be.equal(thriftLib.msgTypes.MsgType.ERROR);
            result.type.should.be.equal(thriftLib.msgTypes.MsgType.SEARCH_ES_RESP);
            console.log('SEARCH ES TWITTER REQ PASSED');
        }
    }
};


var getEsReq = new thriftLib.esTypes.GetEsReq({indexName: 'document', type: 'article',
    ids: ['000000b8-7f59-49ad-b9bc-e92aa858fc37', '0000003d-5ce0-4eed-a56f-bbd3f8d8242b_XXX']});
var getEsReqTest = {
    'GET ES REQ TEST': {
        topic: function() {
            test(thriftLib.serializeMsg(thriftLib.msgTypes.MsgType.GET_ES_REQ, getEsReq, 2236, '7179266c-be35-4e20-a1ee-0547e5c7fb88'), this.callback);
        }, 'Result': function(err, result) {
            should.not.exist(err);
            result.type.should.not.be.equal(thriftLib.msgTypes.MsgType.ERROR);
            result.type.should.be.equal(thriftLib.msgTypes.MsgType.GET_ES_RESP);
            console.log('GET ES REQ PASSED');
        }
    }
};


client.on('error', function(err) {
    console.log('ZmqClient error', err);
});

function test(data, cb) {
    client.request('es', data,
        function(err, data) { // ignore partial as it is not supported

        },
        function(err, data) {
            if (err)
                cb(err, null);
            else
                processData(data, cb);
        },
        { timeout: 10000, retry: 1, nocache: 0 }
    );
}

function processData(data, cb) {
    var msg = thriftLib.deserializeMsg(new Buffer(data));
    cb(null, msg);
}

var combinedTests = extend({}, searchEsTwitterReqTest /*searchEsReqTest, getEsReqTest, searchEsReqTest*/);

suite.addBatch(combinedTests);

suite.run();

function extend(target) {
    var sources = [].slice.call(arguments, 1);
    sources.forEach(function (source) {
        for (var prop in source) {
            target[prop] = source[prop];
        }
    });
    return target;
}

