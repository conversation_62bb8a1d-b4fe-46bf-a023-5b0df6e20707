{"name": "es_ms", "version": "0.1.0", "author": "<PERSON><PERSON><PERSON>", "description": "ElasticSearch micro service", "scripts": {"start": "node app.js", "test": "node test/EsWorkerTest.js"}, "dependencies": {"app-module-path": "^1.0.4", "bluebird": "^3.1.1", "cassandra-driver": "^3.4.1", "date-format": "0.0.2", "debug": "~2.2.0", "efk_logger": "git+ssh://******************************:221/andrej/efk_logger.git#prod", "elasticsearch": "^11.0.1", "glob": "~4.3.1", "hbs": "~3.1.0", "logger": "git+ssh://******************************:221/andrej/logger.git#prod", "method-override": "~2.3.0", "moment": "~2.29.1", "ms_config": "git+ssh://******************************:221/andrej/ms_config.git#prod", "node-uuid": "~1.4.3", "pigato": "", "should": "^8.1.1", "thrift_lib": "git+ssh://******************************:221/andrej/thrift_lib.git#prod", "vows": "^0.8.1", "winston": "^0.9.0"}}